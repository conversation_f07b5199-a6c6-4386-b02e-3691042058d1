# AI Agent Requirements: OpenCart Dual Currency Mod Fix

## Overview
This document provides comprehensive requirements for an AI agent to diagnose and fix OpenCart dual currency modification (ocmod) files that fail with "NOT FOUND - OPERATIONS ABORTED!" errors.

## Problem Identification

### Common Error Patterns
```
MOD: Dual Currency Final (for Custom Themes)
FILE: catalog/controller/product/product.php
CODE: [specific code pattern]
LINE: [line number]
NOT FOUND - OPERATIONS ABORTED!
```

### Root Causes
1. **Indentation Mismatches**: Search patterns use wrong tab/space combinations
2. **Version Incompatibility**: Code structure changed between OpenCart versions
3. **Narrow Search Patterns**: Single-line searches instead of context blocks
4. **Exact Line Dependencies**: Mods expecting specific line numbers

## Required Analysis Steps

### 1. File Structure Analysis
- Examine actual indentation in target files (tabs vs spaces)
- Identify current code structure vs expected patterns
- Check OpenCart version compatibility
- Verify file paths exist and are accessible

### 2. Search Pattern Validation
For each failing file, verify:
- Exact indentation (count tabs/spaces)
- Complete code context (multi-line blocks)
- Variable names and syntax
- Code structure changes

### 3. Pattern Matching Strategy
- Use multi-line search blocks instead of single lines
- Include surrounding context for reliability
- Account for minor formatting differences
- Avoid line-number dependencies

## File-Specific Requirements

### Controller Files (PHP)
**Target Files:**
- `catalog/controller/product/product.php`
- `catalog/controller/product/category.php`
- `catalog/controller/product/search.php`
- `catalog/controller/product/manufacturer.php`
- `catalog/controller/product/special.php`
- `catalog/controller/checkout/cart.php`
- `extension/opencart/catalog/controller/module/bestseller.php`
- `extension/opencart/catalog/controller/module/featured.php`
- `extension/opencart/catalog/controller/module/latest.php`
- `extension/opencart/catalog/controller/module/special.php`

**Required Modifications:**
1. **Price Calculation**: Add EUR price alongside main currency
2. **Special Price Handling**: Include EUR special prices when applicable
3. **Tax Calculations**: Ensure proper tax handling for both currencies
4. **Data Array Updates**: Add `price_eur` and `special_eur` to data arrays

### Template Files (Twig)
**Target Files:**
- `catalog/view/theme/[THEME]/template/product/product.twig`
- `catalog/view/theme/[THEME]/template/product/list.twig`
- `catalog/view/theme/[THEME]/template/checkout/cart.twig`
- `catalog/view/theme/[THEME]/template/checkout/cart_list.twig`

**Required Modifications:**
1. **Display Logic**: Show EUR prices in parentheses when different from main currency
2. **Conditional Rendering**: Only display EUR when it differs from primary currency
3. **Styling**: Apply consistent styling (text-muted, smaller font)
4. **Raw Output**: Use `|raw` filter for HTML content in totals

## Technical Implementation Requirements

### Search Pattern Format
```xml
<search><![CDATA[
[Complete multi-line code block with exact indentation]
]]></search>
```

### Modification Types
1. **position="after"**: Add new code after existing block
2. **position="before"**: Add new code before existing block  
3. **position="replace"**: Replace entire code block

### Code Injection Standards
```php
// Dual Currency: Add EUR price
if ($this->customer->isLogged() || !$this->config->get('config_customer_price')) {
    $data['price_eur'] = $this->currency->format($this->tax->calculate($product_info['price'], $product_info['tax_class_id'], $this->config->get('config_tax')), 'EUR');
} else {
    $data['price_eur'] = false;
}
```

### Template Display Logic
```twig
{% if price_eur and price != price_eur %}
<span class="text-muted" style="font-size: 0.9em; display: block;">({{ price_eur }})</span>
{% endif %}
```

## Validation Requirements

### Pre-Implementation Checks
1. **File Existence**: Verify all target files exist
2. **Pattern Matching**: Test search patterns against actual files
3. **Indentation Verification**: Confirm exact whitespace matching
4. **Syntax Validation**: Ensure PHP/Twig syntax correctness

### Post-Implementation Testing
1. **Mod Installation**: Verify mod installs without errors
2. **Functionality Testing**: Confirm dual currency display works
3. **Performance Check**: Ensure no performance degradation
4. **Cross-Page Validation**: Test on all affected pages

## Error Handling

### Common Failure Points
1. **Indentation Errors**: Mixed tabs/spaces
2. **Context Mismatches**: Insufficient surrounding code
3. **Variable Scope Issues**: Incorrect variable references
4. **Template Syntax**: Twig syntax errors

### Debugging Approach
1. **Incremental Testing**: Fix one file at a time
2. **Pattern Isolation**: Test search patterns individually
3. **Manual Verification**: Compare against actual file content
4. **Rollback Strategy**: Maintain backup of original files

## Success Criteria

### Functional Requirements
- ✅ Mod installs without "NOT FOUND" errors
- ✅ EUR prices display alongside main currency
- ✅ Prices only show when different from main currency
- ✅ Tax calculations work correctly for both currencies
- ✅ All product pages, listings, cart, and modules show dual currency
- ✅ Template rendering works without errors

### Quality Standards
- ✅ Clean, readable code injection
- ✅ Consistent styling across all pages
- ✅ No performance impact
- ✅ Proper error handling
- ✅ Maintainable code structure

## Deliverables

### Required Outputs
1. **Fixed ocmod.xml file** with working search patterns
2. **Documentation** explaining changes made
3. **Installation instructions** for end users
4. **Testing checklist** for validation
5. **Backup strategy** for rollback if needed

### File Naming Convention
- `dual_currency_fixed.ocmod.xml` - Fixed version
- `dual_currency_backup.ocmod.xml` - Original backup
- `DUAL_CURRENCY_FIX_README.md` - Documentation

## Agent Instructions

### Step-by-Step Process
1. **Analyze Error Log**: Identify all failing files and patterns
2. **Examine Target Files**: Check actual code structure and indentation
3. **Create Search Patterns**: Build multi-line context blocks
4. **Test Patterns**: Verify each search pattern matches exactly
5. **Implement Fixes**: Apply modifications with proper code injection
6. **Validate Results**: Ensure mod installs and functions correctly
7. **Document Changes**: Create comprehensive documentation
8. **Provide Instructions**: Give clear installation steps

### Quality Assurance
- Always use exact indentation from target files
- Include sufficient context in search patterns
- Test each modification independently
- Maintain original functionality while adding dual currency
- Ensure backward compatibility
- Follow OpenCart coding standards

## Code Examples

### Typical Search Pattern (WRONG)
```xml
<search><![CDATA['price' => $price,]]></search>
```

### Correct Search Pattern (RIGHT)
```xml
<search><![CDATA[			$product_data = [
				'description' => $description,
				'thumb'       => $this->model_tool_image->resize($image, $this->config->get('config_image_product_width'), $this->config->get('config_image_product_height')),
				'price'       => $price,
				'special'     => $special,
				'tax'         => $tax,
				'minimum'     => $result['minimum'] > 0 ? $result['minimum'] : 1,
				'href'        => $this->url->link('product/product', 'language=' . $this->config->get('config_language') . '&product_id=' . $result['product_id'] . $url)
			] + $result;]]></search>
```

### Complete Modification Example
```xml
<file path="catalog/controller/product/category.php">
    <operation>
        <search><![CDATA[			$product_data = [
				'description' => $description,
				'thumb'       => $this->model_tool_image->resize($image, $this->config->get('config_image_product_width'), $this->config->get('config_image_product_height')),
				'price'       => $price,
				'special'     => $special,
				'tax'         => $tax,
				'minimum'     => $result['minimum'] > 0 ? $result['minimum'] : 1,
				'href'        => $this->url->link('product/product', 'language=' . $this->config->get('config_language') . '&product_id=' . $result['product_id'] . $url)
			] + $result;]]></search>
        <add position="before"><![CDATA[
        // Dual Currency: Add EUR price
        if (is_numeric($result['special'])) {
            $special_eur = $this->currency->format($this->tax->calculate($result['special'], $result['tax_class_id'], $this->config->get('config_tax')), 'EUR');
        } else {
            $special_eur = false;
        }

        if ($this->config->get('config_tax')) {
            $price_eur = $this->currency->format($this->tax->calculate($result['price'], $result['tax_class_id'], $this->config->get('config_tax')), 'EUR');
        } else {
            $price_eur = $this->currency->format($result['price'], 'EUR');
        }

        ]]></add>
        <add position="replace"><![CDATA[			$product_data = [
				'description' => $description,
				'thumb'       => $this->model_tool_image->resize($image, $this->config->get('config_image_product_width'), $this->config->get('config_image_product_height')),
				'price'       => $price,
				'price_eur'   => $price_eur,
				'special'     => $special,
				'special_eur' => $special_eur,
				'tax'         => $tax,
				'minimum'     => $result['minimum'] > 0 ? $result['minimum'] : 1,
				'href'        => $this->url->link('product/product', 'language=' . $this->config->get('config_language') . '&product_id=' . $result['product_id'] . $url)
			] + $result;]]></add>
    </operation>
</file>
```

## Troubleshooting Guide

### Common Issues and Solutions

#### Issue: "NOT FOUND - OPERATIONS ABORTED!"
**Cause**: Search pattern doesn't match actual file content
**Solution**:
1. View actual file content around the target area
2. Copy exact indentation and code structure
3. Use multi-line context blocks
4. Test pattern matching before implementing

#### Issue: Indentation Mismatch
**Cause**: Mixed tabs and spaces or wrong indentation level
**Solution**:
1. Use view tool to see exact whitespace
2. Count tabs vs spaces carefully
3. Match indentation exactly as it appears in file

#### Issue: Variable Scope Errors
**Cause**: Using wrong variable names in different contexts
**Solution**:
1. Check variable names in each file context
2. Use `$result` for listing pages, `$product` for cart, `$product_info` for product page
3. Verify array structure before accessing properties

#### Issue: Template Rendering Errors
**Cause**: Twig syntax errors or missing variables
**Solution**:
1. Ensure variables are passed from controller
2. Use proper Twig conditional syntax
3. Test template changes incrementally

### Debugging Commands
```bash
# View file with line numbers
cat -n filename.php

# Check indentation
sed 's/\t/[TAB]/g' filename.php | sed 's/ /[SPACE]/g'

# Search for specific patterns
grep -n "pattern" filename.php
```

## Version Compatibility

### OpenCart 3.x
- File paths: `catalog/controller/`
- Module paths: `extension/opencart/catalog/controller/module/`
- Template paths: `catalog/view/theme/[theme]/template/`

### OpenCart 4.x
- Similar structure but may have different method signatures
- Check for updated currency and tax calculation methods
- Verify template syntax compatibility

## Final Checklist

### Before Deployment
- [ ] All search patterns tested and verified
- [ ] Code injection follows OpenCart standards
- [ ] No syntax errors in PHP or Twig code
- [ ] Backup of original files created
- [ ] Documentation completed

### After Deployment
- [ ] Mod installs without errors
- [ ] Dual currency displays correctly
- [ ] All pages tested (product, category, cart, checkout)
- [ ] Performance impact assessed
- [ ] User acceptance testing completed

## Support Information

### Key Files to Monitor
- `system/dual_currency.ocmod.xml` - Main modification file
- `system/storage/logs/ocmod.log` - Installation log
- Error logs for runtime issues

### Common User Questions
1. **"Why don't I see EUR prices?"** - Check if EUR differs from main currency
2. **"Prices look wrong"** - Verify tax calculation settings
3. **"Mod won't install"** - Check search pattern matching
4. **"Performance issues"** - Review currency calculation efficiency
