# Dual Currency Mod Fix

## Problem
The original dual currency mod (`system/dual_currency.ocmod.xml`) was failing with the following errors:

```
2025-08-21 22:41:44 - MOD: Dual Currency Final (for Custom Themes)

FILE: catalog/controller/product/product.php
CODE: $data['price'] = $this->currency->format($this->tax->calculate($product_info['price'], $product_info['tax_class_id'], $this->config->get('config_tax')), $this->session->data['currency']);
LINE: 339
CODE: $data['special'] = $this->currency->format($this->tax->calculate($product_info['special'], $product_info['tax_class_id'], $this->config->get('config_tax')), $this->session->data['currency']);
LINE: 353

[... more files with similar errors ...]

NOT FOUND - OPERATIONS ABORTED!
```

## Root Cause
The mod was written for a different version of OpenCart and was looking for exact code patterns that didn't match the current codebase. The main issues were:

1. **Incorrect indentation**: The mod was looking for code with different tab/space indentation
2. **Exact line number expectations**: The mod expected code to be on specific lines
3. **Incomplete code blocks**: Some search patterns were too narrow and didn't match the full context

## Solution
I've created a fixed version of the mod that:

1. **Corrected indentation patterns**: Updated all search patterns to match the actual indentation in the current files
2. **Used broader context searches**: Instead of single lines, used multi-line blocks for more reliable matching
3. **Fixed cart controller logic**: Completely rewrote the cart controller modifications to work with the current structure
4. **Maintained all original functionality**: The mod still adds EUR prices alongside the main currency

## Files Fixed

### Controller Files (PHP Logic)
- `catalog/controller/product/product.php` - Product detail page
- `catalog/controller/product/category.php` - Category listing page  
- `catalog/controller/product/search.php` - Search results page
- `catalog/controller/product/manufacturer.php` - Manufacturer page
- `catalog/controller/product/special.php` - Special offers page
- `catalog/controller/checkout/cart.php` - Shopping cart
- `extension/opencart/catalog/controller/module/bestseller.php` - Bestseller module
- `extension/opencart/catalog/controller/module/featured.php` - Featured module
- `extension/opencart/catalog/controller/module/latest.php` - Latest products module
- `extension/opencart/catalog/controller/module/special.php` - Special offers module

### Template Files (Twig Views)
- `catalog/view/theme/dc_minimal/template/product/product.twig` - Product detail template
- `catalog/view/theme/dc_minimal/template/product/list.twig` - Product listing template
- `catalog/view/theme/dc_minimal/template/checkout/cart.twig` - Cart template
- `catalog/view/theme/dc_minimal/template/checkout/cart_list.twig` - Cart list template

## What the Mod Does

The dual currency mod adds EUR prices alongside the main currency (BGN) throughout the store:

1. **Product pages**: Shows EUR price below the main price
2. **Category/listing pages**: Shows EUR prices for all products
3. **Shopping cart**: Shows EUR prices for individual items and totals
4. **Modules**: Adds EUR prices to bestseller, featured, latest, and special offer modules

## Installation Instructions

### Method 1: Direct File Replacement (Recommended for testing)
1. Backup the original: `copy system\dual_currency.ocmod.xml system\dual_currency_original_backup.ocmod.xml`
2. The fixed version is already in place as `system\dual_currency.ocmod.xml`

### Method 2: Through OpenCart Admin Panel
1. Go to your OpenCart admin panel (`/mez_adm/`)
2. Navigate to **Extensions > Modifications**
3. If the old mod is installed, delete it first
4. Click **Refresh** to clear any cached modifications
5. Upload the new mod file through **Extensions > Installer**
6. Go back to **Extensions > Modifications** 
7. Click the blue **Refresh** button to apply the changes

### Method 3: Manual Installation (Advanced)
If you want to apply the changes manually without using the mod system:
1. Edit each PHP file listed above
2. Add the dual currency code as shown in the mod file
3. Update the Twig templates to display the EUR prices

## Testing

After installation, you should see:
- EUR prices displayed in parentheses next to BGN prices
- EUR prices only show when they differ from the main currency
- All product listings, cart, and checkout pages show dual currency
- The EUR prices are properly calculated with taxes

## Backup Files Created
- `system/dual_currency_backup.ocmod.xml` - Original mod file
- `system/dual_currency_fixed.ocmod.xml` - Fixed version (same as current dual_currency.ocmod.xml)

## Technical Details

The mod works by:
1. **Adding EUR price calculations** in all product controllers
2. **Passing EUR prices to templates** via the data array
3. **Displaying EUR prices conditionally** in Twig templates
4. **Handling tax calculations** properly for both currencies
5. **Formatting prices** using OpenCart's currency formatting system

The EUR prices are calculated in real-time using OpenCart's built-in currency conversion system, ensuring they're always up-to-date with current exchange rates.
