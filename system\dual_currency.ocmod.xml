<?xml version="1.0" encoding="utf-8"?>
<modification>
    <name>Dual Currency Final (for Custom Themes) - Fixed</name>
    <code>dual_currency_final_custom_fixed</code>
    <version>7.5.2</version>
    <author>Dimitar Iliev - Fixed by AI</author>
    <link></link>

    <!--
    ================================================================
    CONTROLLERS (PHP LOGIC)
    ================================================================
    -->
    <file path="catalog/controller/product/product.php">
        <operation>
            <search><![CDATA[		if ($this->customer->isLogged() || !$this->config->get('config_customer_price')) {
			$data['price'] = $this->currency->format($this->tax->calculate($product_info['price'], $product_info['tax_class_id'], $this->config->get('config_tax')), $this->session->data['currency']);
		} else {
			$data['price'] = false;
		}]]></search>
            <add position="after"><![CDATA[

            // Dual Currency: Add EUR price
            if ($this->customer->isLogged() || !$this->config->get('config_customer_price')) {
                $data['price_eur'] = $this->currency->format($this->tax->calculate($product_info['price'], $product_info['tax_class_id'], $this->config->get('config_tax')), 'EUR');
            } else {
                $data['price_eur'] = false;
            }
            ]]></add>
        </operation>
        <operation>
            <search><![CDATA[		if ((float)$product_info['special']) {
			$data['special'] = $this->currency->format($this->tax->calculate($product_info['special'], $product_info['tax_class_id'], $this->config->get('config_tax')), $this->session->data['currency']);
		} else {
			$data['special'] = false;
		}]]></search>
            <add position="after"><![CDATA[

            // Dual Currency: Add EUR special price
            if ((float)$product_info['special']) {
                $data['special_eur'] = $this->currency->format($this->tax->calculate($product_info['special'], $product_info['tax_class_id'], $this->config->get('config_tax')), 'EUR');
            } else {
                $data['special_eur'] = false;
            }
            ]]></add>
        </operation>
    </file>

    <!-- Product listing pages -->
    <file path="catalog/controller/product/category.php">
        <operation>
            <search><![CDATA[			$product_data = [
				'description' => $description,
				'thumb'       => $this->model_tool_image->resize($image, $this->config->get('config_image_product_width'), $this->config->get('config_image_product_height')),
				'price'       => $price,
				'special'     => $special,
				'tax'         => $tax,
				'minimum'     => $result['minimum'] > 0 ? $result['minimum'] : 1,
				'href'        => $this->url->link('product/product', 'language=' . $this->config->get('config_language') . '&product_id=' . $result['product_id'] . $url)
			] + $result;]]></search>
            <add position="before"><![CDATA[
            // Dual Currency: Add EUR price
            if (is_numeric($result['special'])) {
                $special_eur = $this->currency->format($this->tax->calculate($result['special'], $result['tax_class_id'], $this->config->get('config_tax')), 'EUR');
            } else {
                $special_eur = false;
            }

            if ($this->config->get('config_tax')) {
                $price_eur = $this->currency->format($this->tax->calculate($result['price'], $result['tax_class_id'], $this->config->get('config_tax')), 'EUR');
            } else {
                $price_eur = $this->currency->format($result['price'], 'EUR');
            }

            ]]></add>
            <add position="replace"><![CDATA[			$product_data = [
				'description' => $description,
				'thumb'       => $this->model_tool_image->resize($image, $this->config->get('config_image_product_width'), $this->config->get('config_image_product_height')),
				'price'       => $price,
				'price_eur'   => $price_eur,
				'special'     => $special,
				'special_eur' => $special_eur,
				'tax'         => $tax,
				'minimum'     => $result['minimum'] > 0 ? $result['minimum'] : 1,
				'href'        => $this->url->link('product/product', 'language=' . $this->config->get('config_language') . '&product_id=' . $result['product_id'] . $url)
			] + $result;]]></add>
        </operation>
    </file>

    <file path="catalog/controller/product/search.php">
        <operation>
            <search><![CDATA[			$product_data = [
				'thumb'       => $this->model_tool_image->resize($image, $this->config->get('config_image_product_width'), $this->config->get('config_image_product_height')),
				'description' => $description,
				'price'       => $price,
				'special'     => $special,
				'tax'         => $tax,
				'minimum'     => $result['minimum'] > 0 ? $result['minimum'] : 1,
				'href'        => $this->url->link('product/product', 'language=' . $this->config->get('config_language') . '&product_id=' . $result['product_id'] . $url)
			] + $result;]]></search>
            <add position="before"><![CDATA[
            // Dual Currency: Add EUR price
            if (is_numeric($result['special'])) {
                $special_eur = $this->currency->format($this->tax->calculate($result['special'], $result['tax_class_id'], $this->config->get('config_tax')), 'EUR');
            } else {
                $special_eur = false;
            }

            if ($this->config->get('config_tax')) {
                $price_eur = $this->currency->format($this->tax->calculate($result['price'], $result['tax_class_id'], $this->config->get('config_tax')), 'EUR');
            } else {
                $price_eur = $this->currency->format($result['price'], 'EUR');
            }

            ]]></add>
            <add position="replace"><![CDATA[			$product_data = [
				'thumb'       => $this->model_tool_image->resize($image, $this->config->get('config_image_product_width'), $this->config->get('config_image_product_height')),
				'description' => $description,
				'price'       => $price,
				'price_eur'   => $price_eur,
				'special'     => $special,
				'special_eur' => $special_eur,
				'tax'         => $tax,
				'minimum'     => $result['minimum'] > 0 ? $result['minimum'] : 1,
				'href'        => $this->url->link('product/product', 'language=' . $this->config->get('config_language') . '&product_id=' . $result['product_id'] . $url)
			] + $result;]]></add>
        </operation>
    </file>

    <file path="catalog/controller/product/manufacturer.php">
        <operation>
            <search><![CDATA[			$product_data = [
				'thumb'       => $this->model_tool_image->resize($image, $this->config->get('config_image_product_width'), $this->config->get('config_image_product_height')),
				'description' => $description,
				'price'       => $price,
				'special'     => $special,
				'tax'         => $tax,
				'minimum'     => $result['minimum'] > 0 ? $result['minimum'] : 1,
				'href'        => $this->url->link('product/product', 'language=' . $this->config->get('config_language') . '&manufacturer_id=' . $result['manufacturer_id'] . '&product_id=' . $result['product_id'] . $url)
			] + $result;]]></search>
            <add position="before"><![CDATA[
            // Dual Currency: Add EUR price
            if (is_numeric($result['special'])) {
                $special_eur = $this->currency->format($this->tax->calculate($result['special'], $result['tax_class_id'], $this->config->get('config_tax')), 'EUR');
            } else {
                $special_eur = false;
            }

            if ($this->config->get('config_tax')) {
                $price_eur = $this->currency->format($this->tax->calculate($result['price'], $result['tax_class_id'], $this->config->get('config_tax')), 'EUR');
            } else {
                $price_eur = $this->currency->format($result['price'], 'EUR');
            }

            ]]></add>
            <add position="replace"><![CDATA[			$product_data = [
				'thumb'       => $this->model_tool_image->resize($image, $this->config->get('config_image_product_width'), $this->config->get('config_image_product_height')),
				'description' => $description,
				'price'       => $price,
				'price_eur'   => $price_eur,
				'special'     => $special,
				'special_eur' => $special_eur,
				'tax'         => $tax,
				'minimum'     => $result['minimum'] > 0 ? $result['minimum'] : 1,
				'href'        => $this->url->link('product/product', 'language=' . $this->config->get('config_language') . '&manufacturer_id=' . $result['manufacturer_id'] . '&product_id=' . $result['product_id'] . $url)
			] + $result;]]></add>
        </operation>
    </file>

    <file path="catalog/controller/product/special.php">
        <operation>
            <search><![CDATA[			$product_data = [
				'thumb'       => $this->model_tool_image->resize($image, $this->config->get('config_image_product_width'), $this->config->get('config_image_product_height')),
				'description' => $description,
				'price'       => $price,
				'special'     => $special,
				'tax'         => $tax,
				'minimum'     => $result['minimum'] > 0 ? $result['minimum'] : 1,
				'href'        => $this->url->link('product/product', 'language=' . $this->config->get('config_language') . '&product_id=' . $result['product_id'] . $url)
			] + $result;]]></search>
            <add position="before"><![CDATA[
            // Dual Currency: Add EUR price
            if (is_numeric($result['special'])) {
                $special_eur = $this->currency->format($this->tax->calculate($result['special'], $result['tax_class_id'], $this->config->get('config_tax')), 'EUR');
            } else {
                $special_eur = false;
            }

            if ($this->config->get('config_tax')) {
                $price_eur = $this->currency->format($this->tax->calculate($result['price'], $result['tax_class_id'], $this->config->get('config_tax')), 'EUR');
            } else {
                $price_eur = $this->currency->format($result['price'], 'EUR');
            }

            ]]></add>
            <add position="replace"><![CDATA[			$product_data = [
				'thumb'       => $this->model_tool_image->resize($image, $this->config->get('config_image_product_width'), $this->config->get('config_image_product_height')),
				'description' => $description,
				'price'       => $price,
				'price_eur'   => $price_eur,
				'special'     => $special,
				'special_eur' => $special_eur,
				'tax'         => $tax,
				'minimum'     => $result['minimum'] > 0 ? $result['minimum'] : 1,
				'href'        => $this->url->link('product/product', 'language=' . $this->config->get('config_language') . '&product_id=' . $result['product_id'] . $url)
			] + $result;]]></add>
        </operation>
    </file>

    <!-- Module files -->
    <file path="extension/opencart/catalog/controller/module/bestseller.php">
        <operation>
            <search><![CDATA[				$product_data = [
					'product_id'  => $result['product_id'],
					'thumb'       => $image,
					'name'        => $result['name'],
					'description' => oc_substr(trim(strip_tags(html_entity_decode($result['description'], ENT_QUOTES, 'UTF-8'))), 0, $this->config->get('config_product_description_length')) . '..',
					'price'       => $price,
					'special'     => $special,
					'tax'         => $tax,
					'minimum'     => $result['minimum'] > 0 ? $result['minimum'] : 1,
					'rating'      => $result['rating'],
					'href'        => $this->url->link('product/product', 'language=' . $this->config->get('config_language') . '&product_id=' . $result['product_id'])
				] + $result;]]></search>
            <add position="before"><![CDATA[
            // Dual Currency: Add EUR price
            if (is_numeric($result['special'])) {
                $special_eur = $this->currency->format($this->tax->calculate($result['special'], $result['tax_class_id'], $this->config->get('config_tax')), 'EUR');
            } else {
                $special_eur = false;
            }

            if ($this->config->get('config_tax')) {
                $price_eur = $this->currency->format($this->tax->calculate($result['price'], $result['tax_class_id'], $this->config->get('config_tax')), 'EUR');
            } else {
                $price_eur = $this->currency->format($result['price'], 'EUR');
            }

            ]]></add>
            <add position="replace"><![CDATA[				$product_data = [
					'product_id'  => $result['product_id'],
					'thumb'       => $image,
					'name'        => $result['name'],
					'description' => oc_substr(trim(strip_tags(html_entity_decode($result['description'], ENT_QUOTES, 'UTF-8'))), 0, $this->config->get('config_product_description_length')) . '..',
					'price'       => $price,
					'price_eur'   => $price_eur,
					'special'     => $special,
					'special_eur' => $special_eur,
					'tax'         => $tax,
					'minimum'     => $result['minimum'] > 0 ? $result['minimum'] : 1,
					'rating'      => $result['rating'],
					'href'        => $this->url->link('product/product', 'language=' . $this->config->get('config_language') . '&product_id=' . $result['product_id'])
				] + $result;]]></add>
        </operation>
    </file>

    <file path="extension/opencart/catalog/controller/module/featured.php">
        <operation>
            <search><![CDATA[				$product_data = [
					'product_id'  => $product['product_id'],
					'thumb'       => $image,
					'name'        => $product['name'],
					'description' => oc_substr(trim(strip_tags(html_entity_decode($product['description'], ENT_QUOTES, 'UTF-8'))), 0, $this->config->get('config_product_description_length')) . '..',
					'price'       => $price,
					'special'     => $special,
					'tax'         => $tax,
					'minimum'     => $product['minimum'] > 0 ? $product['minimum'] : 1,
					'rating'      => (int)$product['rating'],
					'href'        => $this->url->link('product/product', 'language=' . $this->config->get('config_language') . '&product_id=' . $product['product_id'])
				] + $product;]]></search>
            <add position="before"><![CDATA[
            // Dual Currency: Add EUR price
            if (is_numeric($product['special'])) {
                $special_eur = $this->currency->format($this->tax->calculate($product['special'], $product['tax_class_id'], $this->config->get('config_tax')), 'EUR');
            } else {
                $special_eur = false;
            }

            if ($this->config->get('config_tax')) {
                $price_eur = $this->currency->format($this->tax->calculate($product['price'], $product['tax_class_id'], $this->config->get('config_tax')), 'EUR');
            } else {
                $price_eur = $this->currency->format($product['price'], 'EUR');
            }

            ]]></add>
            <add position="replace"><![CDATA[				$product_data = [
					'product_id'  => $product['product_id'],
					'thumb'       => $image,
					'name'        => $product['name'],
					'description' => oc_substr(trim(strip_tags(html_entity_decode($product['description'], ENT_QUOTES, 'UTF-8'))), 0, $this->config->get('config_product_description_length')) . '..',
					'price'       => $price,
					'price_eur'   => $price_eur,
					'special'     => $special,
					'special_eur' => $special_eur,
					'tax'         => $tax,
					'minimum'     => $product['minimum'] > 0 ? $product['minimum'] : 1,
					'rating'      => (int)$product['rating'],
					'href'        => $this->url->link('product/product', 'language=' . $this->config->get('config_language') . '&product_id=' . $product['product_id'])
				] + $product;]]></add>
        </operation>
    </file>

    <file path="extension/opencart/catalog/controller/module/latest.php">
        <operation>
            <search><![CDATA[				$product_data = [
					'product_id'  => $result['product_id'],
					'thumb'       => $image,
					'name'        => $result['name'],
					'description' => oc_substr(trim(strip_tags(html_entity_decode($result['description'], ENT_QUOTES, 'UTF-8'))), 0, $this->config->get('config_product_description_length')) . '..',
					'price'       => $price,
					'special'     => $special,
					'tax'         => $tax,
					'minimum'     => $result['minimum'] > 0 ? $result['minimum'] : 1,
					'rating'      => $result['rating'],
					'href'        => $this->url->link('product/product', 'language=' . $this->config->get('config_language') . '&product_id=' . $result['product_id'])
				] + $result;]]></search>
            <add position="before"><![CDATA[
            // Dual Currency: Add EUR price
            if (is_numeric($result['special'])) {
                $special_eur = $this->currency->format($this->tax->calculate($result['special'], $result['tax_class_id'], $this->config->get('config_tax')), 'EUR');
            } else {
                $special_eur = false;
            }

            if ($this->config->get('config_tax')) {
                $price_eur = $this->currency->format($this->tax->calculate($result['price'], $result['tax_class_id'], $this->config->get('config_tax')), 'EUR');
            } else {
                $price_eur = $this->currency->format($result['price'], 'EUR');
            }

            ]]></add>
            <add position="replace"><![CDATA[				$product_data = [
					'product_id'  => $result['product_id'],
					'thumb'       => $image,
					'name'        => $result['name'],
					'description' => oc_substr(trim(strip_tags(html_entity_decode($result['description'], ENT_QUOTES, 'UTF-8'))), 0, $this->config->get('config_product_description_length')) . '..',
					'price'       => $price,
					'price_eur'   => $price_eur,
					'special'     => $special,
					'special_eur' => $special_eur,
					'tax'         => $tax,
					'minimum'     => $result['minimum'] > 0 ? $result['minimum'] : 1,
					'rating'      => $result['rating'],
					'href'        => $this->url->link('product/product', 'language=' . $this->config->get('config_language') . '&product_id=' . $result['product_id'])
				] + $result;]]></add>
        </operation>
    </file>

    <file path="extension/opencart/catalog/controller/module/special.php">
        <operation>
            <search><![CDATA[				$product_data = [
					'product_id'  => $result['product_id'],
					'thumb'       => $image,
					'name'        => $result['name'],
					'description' => oc_substr(trim(strip_tags(html_entity_decode($result['description'], ENT_QUOTES, 'UTF-8'))), 0, $this->config->get('config_product_description_length')) . '..',
					'price'       => $price,
					'special'     => $special,
					'tax'         => $tax,
					'minimum'     => $result['minimum'] > 0 ? $result['minimum'] : 1,
					'rating'      => $result['rating'],
					'href'        => $this->url->link('product/product', 'language=' . $this->config->get('config_language') . '&product_id=' . $result['product_id'])
				] + $result;]]></search>
            <add position="before"><![CDATA[
            // Dual Currency: Add EUR price
            if (is_numeric($result['special'])) {
                $special_eur = $this->currency->format($this->tax->calculate($result['special'], $result['tax_class_id'], $this->config->get('config_tax')), 'EUR');
            } else {
                $special_eur = false;
            }

            if ($this->config->get('config_tax')) {
                $price_eur = $this->currency->format($this->tax->calculate($result['price'], $result['tax_class_id'], $this->config->get('config_tax')), 'EUR');
            } else {
                $price_eur = $this->currency->format($result['price'], 'EUR');
            }

            ]]></add>
            <add position="replace"><![CDATA[				$product_data = [
					'product_id'  => $result['product_id'],
					'thumb'       => $image,
					'name'        => $result['name'],
					'description' => oc_substr(trim(strip_tags(html_entity_decode($result['description'], ENT_QUOTES, 'UTF-8'))), 0, $this->config->get('config_product_description_length')) . '..',
					'price'       => $price,
					'price_eur'   => $price_eur,
					'special'     => $special,
					'special_eur' => $special_eur,
					'tax'         => $tax,
					'minimum'     => $result['minimum'] > 0 ? $result['minimum'] : 1,
					'rating'      => $result['rating'],
					'href'        => $this->url->link('product/product', 'language=' . $this->config->get('config_language') . '&product_id=' . $result['product_id'])
				] + $result;]]></add>
        </operation>
    </file>

    <!--
    ================================================================
    VIEWS (TWIG FILES - FLEXIBLE SEARCH FOR dc_minimal THEME)
    ================================================================
    -->
    <file path="catalog/view/theme/dc_minimal/template/product/product.twig">
        <operation>
            <search index="0"><![CDATA[{{ price }}]]></search>
            <add position="after"><![CDATA[
            {% if price_eur and price != price_eur %}
            <span class="text-muted" style="font-size: 0.9em; display: block;">({{ price_eur }})</span>
            {% endif %}
            ]]></add>
        </operation>
         <operation>
            <search index="0"><![CDATA[{{ special }}]]></search>
            <add position="after"><![CDATA[
            {% if special_eur and special != special_eur %}
             <span class="text-muted" style="font-size: 0.9em;">({{ special_eur }})</span>
            {% endif %}
            ]]></add>
        </operation>
    </file>

    <file path="catalog/view/theme/dc_minimal/template/product/list.twig">
        <operation>
            <search index="0"><![CDATA[{{ product.price }}]]></search>
            <add position="after"><![CDATA[
              {% if not product.special %}
                {% if product.price_eur and product.price != product.price_eur %}
                  <div class="price-eur text-muted" style="font-size: 0.9em; margin-top: 2px;">({{ product.price_eur }})</div>
                {% endif %}
              {% endif %}
            ]]></add>
        </operation>
        <operation>
            <search index="0"><![CDATA[{{ product.special }}]]></search>
            <add position="after"><![CDATA[
                {% if product.special_eur and product.special != product.special_eur %}
                  <span class="price-new-eur text-muted" style="font-size: 0.9em;">({{ product.special_eur }})</span>
                {% endif %}
            ]]></add>
        </operation>
    </file>

    <file path="catalog/view/theme/dc_minimal/template/checkout/cart.twig">
        <operation>
            <search index="1"><![CDATA[{{ product.price }}]]></search>
            <add position="after"><![CDATA[
            {% if product.price_eur and product.price != product.price_eur %}<br/><small class="text-muted">({{ product.price_eur }})</small>{% endif %}
            ]]></add>
        </operation>
        <operation>
            <search index="0"><![CDATA[{{ product.total }}]]></search>
            <add position="after"><![CDATA[
            {% if product.total_eur and product.total != product.total_eur %}<br/><small class="text-muted">({{ product.total_eur }})</small>{% endif %}
            ]]></add>
        </operation>
        <operation>
            <search><![CDATA[<td>{{ total.text }}</td>]]></search>
            <add position="replace"><![CDATA[
            <td>{{ total.text|raw }}</td>
            ]]></add>
        </operation>
    </file>

    <file path="catalog/view/theme/dc_minimal/template/checkout/cart_list.twig">
        <operation>
            <search><![CDATA[<td>{{ total.title }}</td>]]></search>
            <add position="replace"><![CDATA[
            <td>{{ total.title|raw }}</td>
            ]]></add>
        </operation>
    </file>

</modification>

    <!-- v7.0 TAILORED CART CONTROLLER FIX -->
    <file path="catalog/controller/checkout/cart.php">
        <operation>
            <search><![CDATA[		$data['products'][] = [
			'thumb'        => $this->model_tool_image->resize($product['image'], $this->config->get('config_image_cart_width'), $this->config->get('config_image_cart_height')),
			'subscription' => $subscription,
			'stock'        => $product['stock_status'] ? true : !(!$this->config->get('config_stock_checkout') || $this->config->get('config_stock_warning')),
			'minimum'      => !$product['minimum_status'] ? sprintf($this->language->get('error_minimum'), $product['minimum']) : 0,
			'price'        => $price_status ? $product['price_text'] : '',
			'total'        => $price_status ? $product['total_text'] : '',
			'href'         => $this->url->link('product/product', 'language=' . $this->config->get('config_language') . '&product_id=' . $product['product_id']),
			'remove'       => $this->url->link('checkout/cart.remove', 'language=' . $this->config->get('config_language') . '&key=' . $product['cart_id'])
		] + $product;]]></search>
            <add position="before"><![CDATA[
            // Dual Currency
            if ($price_status) {
                $price_eur = $this->currency->format($this->tax->calculate($product['price'], $product['tax_class_id'], $this->config->get('config_tax')), 'EUR');
                $total_eur = $this->currency->format(($this->tax->calculate($product['price'], $product['tax_class_id'], $this->config->get('config_tax'))) * $product['quantity'], 'EUR');
            } else {
                $price_eur = '';
                $total_eur = '';
            }

            ]]></add>
            <add position="replace"><![CDATA[		$data['products'][] = [
			'thumb'        => $this->model_tool_image->resize($product['image'], $this->config->get('config_image_cart_width'), $this->config->get('config_image_cart_height')),
			'subscription' => $subscription,
			'stock'        => $product['stock_status'] ? true : !(!$this->config->get('config_stock_checkout') || $this->config->get('config_stock_warning')),
			'minimum'      => !$product['minimum_status'] ? sprintf($this->language->get('error_minimum'), $product['minimum']) : 0,
			'price'        => $price_status ? $product['price_text'] : '',
			'price_eur'    => $price_status ? $price_eur : '',
			'total'        => $price_status ? $product['total_text'] : '',
			'total_eur'    => $price_status ? $total_eur : '',
			'href'         => $this->url->link('product/product', 'language=' . $this->config->get('config_language') . '&product_id=' . $product['product_id']),
			'remove'       => $this->url->link('checkout/cart.remove', 'language=' . $this->config->get('config_language') . '&key=' . $product['cart_id'])
		] + $product;]]></add>
        </operation>
        <operation>
            <search><![CDATA[		// Display prices
		if ($this->customer->isLogged() || !$this->config->get('config_customer_price')) {
			($this->model_checkout_cart->getTotals)($totals, $taxes, $total);

			foreach ($totals as $result) {
				$data['totals'][] = ['text' => $price_status ? $this->currency->format($result['value'], $this->session->data['currency']) : ''] + $result;
			}
		}]]></search>
            <add position="replace"><![CDATA[		// Display prices
		if ($this->customer->isLogged() || !$this->config->get('config_customer_price')) {
			($this->model_checkout_cart->getTotals)($totals, $taxes, $total);

			foreach ($totals as $result) {
				if ($price_status) {
					$text_bgn = $this->currency->format($result['value'], $this->session->data['currency']);
					$text_eur = $this->currency->format($result['value'], 'EUR');

					$final_text = $text_bgn;
					if ($text_bgn != $text_eur) {
						 $final_text .= ' <br/><small class="text-muted">(' . $text_eur . ')</small>';
					}

					$data['totals'][] = ['text' => $final_text] + $result;
				} else {
					$data['totals'][] = ['text' => ''] + $result;
				}
			}
		}]]></add>
        </operation>
    </file>

    <!--
    ================================================================
    VIEWS (TWIG FILES - FLEXIBLE SEARCH FOR dc_minimal THEME)
    ================================================================
    -->
    <file path="catalog/view/theme/dc_minimal/template/product/product.twig">
        <operation>
            <search index="0"><![CDATA[{{ price }}]]></search>
            <add position="after"><![CDATA[
            {% if price_eur and price != price_eur %}
            <span class="text-muted" style="font-size: 0.9em; display: block;">({{ price_eur }})</span>
            {% endif %}
            ]]></add>
        </operation>
         <operation>
            <search index="0"><![CDATA[{{ special }}]]></search>
            <add position="after"><![CDATA[
            {% if special_eur and special != special_eur %}
             <span class="text-muted" style="font-size: 0.9em;">({{ special_eur }})</span>
            {% endif %}
            ]]></add>
        </operation>
    </file>

    <file path="catalog/view/theme/dc_minimal/template/product/list.twig">
        <operation>
            <search index="0"><![CDATA[{{ product.price }}]]></search>
            <add position="after"><![CDATA[
              {% if not product.special %}
                {% if product.price_eur and product.price != product.price_eur %}
                  <div class="price-eur text-muted" style="font-size: 0.9em; margin-top: 2px;">({{ product.price_eur }})</div>
                {% endif %}
              {% endif %}
            ]]></add>
        </operation>
        <operation>
            <search index="0"><![CDATA[{{ product.special }}]]></search>
            <add position="after"><![CDATA[
                {% if product.special_eur and product.special != product.special_eur %}
                  <span class="price-new-eur text-muted" style="font-size: 0.9em;">({{ product.special_eur }})</span>
                {% endif %}
            ]]></add>
        </operation>
    </file>

    <file path="catalog/view/theme/dc_minimal/template/checkout/cart.twig">
        <operation>
            <search index="1"><![CDATA[{{ product.price }}]]></search>
            <add position="after"><![CDATA[
            {% if product.price_eur and product.price != product.price_eur %}<br/><small class="text-muted">({{ product.price_eur }})</small>{% endif %}
            ]]></add>
        </operation>
        <operation>
            <search index="0"><![CDATA[{{ product.total }}]]></search>
            <add position="after"><![CDATA[
            {% if product.total_eur and product.total != product.total_eur %}<br/><small class="text-muted">({{ product.total_eur }})</small>{% endif %}
            ]]></add>
        </operation>
        <operation>
            <search><![CDATA[<td>{{ total.text }}</td>]]></search>
            <add position="replace"><![CDATA[
            <td>{{ total.text|raw }}</td>
            ]]></add>
        </operation>
    </file>

    <file path="catalog/view/theme/dc_minimal/template/checkout/cart_list.twig">
        <operation>
            <search><![CDATA[<td>{{ total.title }}</td>]]></search>
            <add position="replace"><![CDATA[
            <td>{{ total.title|raw }}</td>
            ]]></add>
        </operation>
    </file>

</modification>